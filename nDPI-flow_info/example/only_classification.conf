#Example of configuration if you are interested ONLY in flow (sub)-classification
#(i.e. no metadata at all and no flow risks)

#No flow risks
--cfg=flow_risk.all,0

#General metadata
--cfg=metadata.tcp_fingerprint,0
#BITTORRENT
--cfg=bittorrent,metadata.hash,0
#SSDP
--cfg=ssdp,metadata,0
#TLS
--cfg=tls,metadata.sha1_fingerprint,0 --cfg=tls,metadata.ja3s_fingerprint,0 --cfg=tls,metadata.ja4c_fingerprint,0 --cfg=tls,metadata.cert_server_names,0 --cfg=tls,metadata.cert_validity,0 --cfg=tls,metadata.cert_issuer,0 --cfg=tls,metadata.cert_subject,0 --cfg=tls,metadata.alpn_negotiated,0 --cfg=tls,metadata.versions_supported,0 --cfg=tls,metadata.cipher,0 --cfg=tls,metadata.browser,0
#SIP
--cfg=sip,metadata.attribute.from,0 --cfg=sip,metadata.attribute.to,0
#STUN
--cfg=stun,metadata.attribute.mapped_address,0 --cfg=stun,metadata.attribute.peer_address,0 --cfg=stun,metadata.attribute.relayed_address,0 --cfg=stun,metadata.attribute.response_origin,0 --cfg=stun,metadata.attribute.other_address,0
#HTTP
--cfg=http,metadata.request_content_type,0 --cfg=http,metadata.referer,0 --cfg=http,metadata.host,0 --cfg=http,metadata.username,0 --cfg=http,metadata.password,0

#DNS:we need only the request for sub-classification
--cfg=dns,process_response,0 #Note that this option has an huge impact on FPC!

#RTP
--cfg=rtp,max_packets_extra_dissection,0


# Configuration knobs

List of the supported configuration options:

| Protocol     | Parameter                                 | Default value | Min value | Max value | Description | Notes  |
| ------       | ------                                    | ------        | ------    | ------    | ------      | ------ |
| NULL         | "packets_limit_per_flow"                  | 32            | 0         | 255       | The upper limit on the number of packets per flow that will be subject to DPI, after which classification will be considered complete (0 = no limit) |
| NULL         | "flow.direction_detection"                | enable        | NULL      | NULL      | Enable/disable internal detection of packet direction (client to server or server to client) |
| NULL         | "flow.track_payload"                      | disable       | NULL      | NULL      | Enable/disable tracking/export of flow payload (i.e. L5/7 data): if enabled, the library exports the first 1024 bytes of payload for each flow  |
| NULL         | "flow.use_client_ip_in_guess"             | enable        | NULL      | NULL      | Use client IP in guesses of flow protocol IDs by IP. |
| NULL         | "flow.use_client_port_in_guess"           | enable        | NULL      | NULL      | Use client port in guesses of flow protocol IDs. |
| NULL         | "tcp_ack_payload_heuristic"               | disable       | NULL      | NULL      | In some networks, there are some anomalous TCP flows where the smallest ACK packets have some kind of zero padding. It looks like the IP and TCP headers in those frames wrongly consider the 0x00 Ethernet padding bytes as part of the TCP payload. While this kind of packets is perfectly valid per-se, in some conditions they might be treated by the TCP reassembler logic as (partial) overlaps, deceiving the classification engine. This parameter enable/disable an heuristic to detect these packets and to ignore them, allowing correct detection/classification. See #1946 for other details |
| NULL         | "fully_encrypted_heuristic"               | disable       | NULL      | NULL      | Enable/disable an heuristic to detect fully encrypted sessions, i.e. flows where every bytes of the payload is encrypted in an attempt to “look like nothing”. This heuristic only analyzes the first packet of the flow. See: https://www.usenix.org/system/files/sec23fall-prepub-234-wu-mingshi.pdf |
| NULL         | "libgcrypt.init"                          | 1             | NULL      | NULL      | Enable/disable initialization of libgcrypt. When using the external libgcrypt (instead of the internal crypto code) the libgcrypt runtime must be initialized. If, for whatever reasons, the application alread does it, nDPI must be told to skip it. Note that, by default, nDPI uses the crypto code and not libgcrypt: in that case this parameter is ignored |
| NULL         | "dpi.compute_entropy"                     | 1             | NULL      | NULL      | Enable/disable computation of flow entropy |
| NULL         | "fpc"                                     | enable        | NULL      | NULL      | Enable/disable First Packet Classification |
| NULL         | "metadata.tcp_fingerprint"                | enable        | NULL      | NULL      | Enable/disable computation and export of TCP fingerprint for all TCP flows
| NULL         | "metadata.tcp_fingerprint_raw"            | disable       | NULL      | NULL      | Enable/disable computation and export of raw TCP fingerprint for all TCP flows
| NULL         | "metadata.tcp_fingerprint_format"         | 0             | 0         | 1         | Specify the format of the TCP fingerprint. Possible values: 0 = native nDPI format, 1 = MuonOF (https://github.com/sundruid/muonfp) |
| NULL         | "dpi.guess_on_giveup"                     | 0x03          | 0x00      | 0x03      | Tell the library to guess flow classification, if any DPI algorithms/logics fail.  The value is a bitmask. Values: 0x0 = disabled; 0x01 = enable guessing by port; 0x02 = enable guessing by ip |
| NULL         | "dpi.guess_ip_before_port"                | disable       | NULL      | NULL      | Enable/disable guessing by IP first when guessing flow classifcation. Disabled = guess by port first. |
| NULL         | "flow_risk.$FLOWRISK_NAME_OR_ID"          | enable        | NULL      | NULL      | Enable/disable the specific flow risk. Use "any" as flow risk name if you want to easily enable/disable all flow risks. The names of the flow risks are available at `src/include/ndpi_typedefs.h`: look for `ndpi_risk_shortnames` |
| NULL         | "flow_risk.$FLOWRISK_NAME_OR_ID.info"     | enable        | NULL      | NULL      | Enable/disable the export of flow risk information, i.e. some strings clarifing some details about the specific flow risk set. Use "any" as flow risk name if you want to easily enable/disable flow info for all flow risks. The names of the flow risks are available at `src/include/ndpi_typedefs.h`: look for `ndpi_risk_shortnames` |
| NULL         | "flow_risk_lists.load"                    | 1             | NULL      | NULL      | Enable/disable loading of every IP addresses lists used to check any flow risks |
| NULL         | "flow_risk.anonymous_subscriber.list.icloudprivaterelay.load" | 1             | NULL      | NULL      | Enable/disable loading of internal iCouldPrivateRealy IP address list used to check `NDPI_ANONYMOUS_SUBSCRIBER` flow risk |
| NULL         | "flow_risk.anonymous_subscriber.list.tor.load"                | 1             | NULL      | NULL      | Enable/disable loading of internal IP address list of TOR exit nodes used to check `NDPI_ANONYMOUS_SUBSCRIBER` flow risk  |
| NULL         | "flow_risk.crawler_bot.list.load"         | 1             | NULL      | NULL      | Enable/disable loading of internal IP address list used to check `NDPI_HTTP_CRAWLER_BOT` flow risk |
| NULL         | "filename.config"                         | NULL          | NULL      | NULL      | Name of the file containing a list of configuration knobs itself (one per line)!. Useful to configure nDPI via text file instead of via API |
| NULL         | "log.level"                               | 0             | 0         | 3         | Configure the log/debug level. Possible values: 0 = error, 1 = trace, 2 = debug, 3 = extra debug |
| NULL         | "lru.$CACHE_NAME.size"                    | See description | 0         | 16777215  | Set the size (in number of elements) of the specified LRU cache (0 = the cache is disabled). The keyword "$CACHE_NAME" is a placeholder for the cache name and the possible values are: ookla, bittorrent, stun, tls_cert, mining, msteams, fpc_dns, signal. The default value is "32768" for the bittorrent and signal cache and "1024" for all the other caches |
| NULL         | "lru.$CACHE_NAME.ttl"                     | See description | 0         | 16777215  | Set the TTL (in seconds) for the elements of the specified LRU cache (0 = the elements never explicitly expire). The keyword "$CACHE_NAME" is a placeholder for the cache name and the possible values are: ookla, bittorrent, stun, tls_cert, mining, msteams, fpc_dns, signal. The default value is "120" for the ookla cache, "60" for the msteams and fpc_dns caches and "300" for all the other caches |
| NULL         | "lru.$CACHE_NAME.scope"                   | 0             | 0         | 1         | Set the scope of the specified LRU cache (0 = the cache is local, 1 = the cache is global). The keyword "$CACHE_NAME" is a placeholder for the cache name and the possible values are: ookla, bittorrent, stun, tls_cert, mining, msteams, fpc_dns, signal. The global scope con be set only if a global context has been initialized |
| "http"       | "metadata.request_content_type"           | enable        | NULL      | NULL      | Enable/disable export of Request Content Type header for HTTP flows. |
| "http"       | "metadata.referer"                        | enable        | NULL      | NULL      | Enable/disable export of Referer header for HTTP flows. |
| "http"       | "metadata.host"                           | enable        | NULL      | NULL      | Enable/disable export of Host header for HTTP flows. |
| "http"       | "metadata.username"                       | enable        | NULL      | NULL      | Enable/disable export of (cleartext) username metadata for HTTP flows. |
| "http"       | "metadata.password"                       | enable        | NULL      | NULL      | Enable/disable export of (cleartext) password metadata for HTTP flows. |
| "tls"        | "certificate_expiration_threshold"        | 30            | 0         | 365       | The threshold (in days) used to trigger the `NDPI_TLS_CERTIFICATE_ABOUT_TO_EXPIRE` flow risk |
| "tls"        | "application_blocks_tracking"             | disable       | NULL      | NULL      | Enable/disable processing of TLS Application Blocks (post handshake) to extract statistical information about the flow |
| "tls  "      | "dpi.heuristics",                         | 0x00          | 0x00      | 0x07      | Enable/disable some heuristics to detect encrypted/obfuscated/proxied TLS flows. The value is a bitmask. Values: 0x0 = disabled; 0x01 = enable basic detection (i.e. encrypted TLS without any encapsulation); 0x02 = enable detection over TLS (i.e. TLS-in-TLS); 0x04 = enable detection over HTTP (i.e. TLS-over-WebSocket). If enabled, some false positives are expected. See: https://www.usenix.org/conference/usenixsecurity24/presentation/xue-fingerprinting |
| "tls  "      | "dpi.heuristics.max_packets_extra_dissection",  | 25            | 0         | 255       | If at least one TLS heuristics is enabled (see `tls,"dpi.heuristics"`, this parameter set the upper limit on the number of packets required/processed for each flow. Higher the value, lower the false positive rate but more packets are required by nDPI for processing. |
| "tls"        | "metadata.sha1_fingerprint"               | enable        | NULL      | NULL      | Enable/disable computation and export of SHA1 fingerprint for TLS flows. Note that if it is disable, the flow risk `NDPI_MALICIOUS_SHA1_CERTIFICATE` is not checked |
| "tls"        | "metadata.versions_supported"             | enable        | NULL      | NULL      | Enable/disable export of supported versions metadata for TLS flows |
| "tls"        | "metadata.alpn_negotiated"                | enable        | NULL      | NULL      | Enable/disable export of negotiated ALPN metadata for TLS flows |
| "tls"        | "metadata.cipher"                         | enable        | NULL      | NULL      | Enable/disable export of negotiated cipher metadata for TLS flows |
| "tls"        | "metadata.cert_server_names"              | enable        | NULL      | NULL      | Enable/disable export of server names list from certificate for TLS flows |
| "tls"        | "metadata.cert_validity"                  | enable        | NULL      | NULL      | Enable/disable export of certificate validity timestamps for TLS flows |
| "tls"        | "metadata.cert_issuer"                    | enable        | NULL      | NULL      | Enable/disable export of certificate issuer metadata for TLS flows |
| "tls"        | "metadata.cert_subject"                   | enable        | NULL      | NULL      | Enable/disable export of certificaste subject metadata for TLS flows |
| "tls"        | "metadata.browser"                        | enable        | NULL      | NULL      | Enable/disable an heurstic to determine the broswer used to generate this TLS flows |
| "tls"        | "metadata.ja3s_fingerprint"               | enable        | NULL      | NULL      | Enable/disable computation and export of JA3S fingerprint for TLS flows |
| "tls"        | "metadata.ja4c_fingerprint"               | enable        | NULL      | NULL      | Enable/disable computation and export of JA4C fingerprint for TLS flows. Note that if it is disable, the flow risk `NDPI_MALICIOUS_FINGERPRINT` is not checked |
| "tls"        | "metadata.ja4r_fingerprint"               | disable       | NULL      | NULL      | Enable/disable computation and export of JA4C fingerprint for TLS flows also in raw format |
| "tls"        | "subclassification"                       | enable        | NULL      | NULL      | Enable/disable sub-classification of TLS/DTLS flows |
| "quic"       | "subclassification"                       | enable        | NULL      | NULL      | Enable/disable sub-classification of QUIC flows |
| "smtp"       | "tls_dissection"                          | enable        | NULL      | NULL      | Enable/disable dissection of TLS packets in cleartext SMTP flows (because of opportunistic TLS, via STARTTLS msg) |
| "imap"       | "tls_dissection"                          | enable        | NULL      | NULL      | Enable/disable dissection of TLS packets in cleartext IMAP flows (because of opportunistic TLS, via STARTTLS msg) |
| "pop"        | "tls_dissection"                          | enable        | NULL      | NULL      | Enable/disable dissection of TLS packets in cleartext POP flows (because of opportunistic TLS, via STARTTLS msg) |
| "ftp"        | "tls_dissection"                          | enable        | NULL      | NULL      | Enable/disable dissection of TLS packets in cleartext FTP flows (because of opportunistic TLS, via AUTH TLS msg) |
| "sip"        | "metadata.attribute.from"                 | enable        | NULL      | NULL      | Enable/disable extraction of "From" header from SIP flows |
| "sip"        | "metadata.attribute.from_imsi"            | enable        | NULL      | NULL      | In a SIP flow, if the "From" header contains a valid IMSI, this option enable/disable the extraction of the IMSI itself |
| "sip"        | "metadata.attribute.to"                   | enable        | NULL      | NULL      | Enable/disable extraction of "To" header from SIP flows |
| "sip"        | "metadata.attribute.to_imsi"              | enable        | NULL      | NULL      | In a SIP flow, if the "To" header contains a valid IMSI, this option enable/disable the extraction of the IMSI itself |
| "stun"       | "max_packets_extra_dissection"            | 4             | 0         | 255       | After a flow has been classified has STUN, nDPI might analyse more packets to look for a sub-classification or for metadata. This parameter set the upper limit on the number of these packets  |
| "stun"       | "tls_dissection"                          | enable        | NULL      | NULL      | Enable/disable dissection of TLS packets multiplexed into STUN flows |
| "stun"       | "metadata.attribute.mapped_address"       | enable        | NULL      | NULL      | Enable/disable extraction of (xor)-mapped-address attribute for STUN flows. If it is disabled, STUN classification might be significant faster |
| "stun"       | "metadata.attribute.response_origin"      | enable        | NULL      | NULL      | Enable/disable extraction of response-origin attribute for STUN flows. If it is disabled, STUN classification might be significant faster |
| "stun"       | "metadata.attribute.other_address"        | enable        | NULL      | NULL      | Enable/disable extraction of other-address attribute for STUN flows. If it is disabled, STUN classification might be significant faster |
| "stun"       | "metadata.attribute.relayed_address"      | enable        | NULL      | NULL      | Enable/disable extraction of (xor-)relayed-address attribute for STUN flows. If it is disabled, STUN classification might be significant faster |
| "stun"       | "metadata.attribute.peer_address"         | enable        | NULL      | NULL      | Enable/disable extraction of (xor-)peer-address attribute for STUN flows. If it is disabled, STUN classification might be significant faster; however sub-classification capability might be negatively impacted |
| "bittorrent" | "metadata.hash"                           | enable        | NULL      | NULL      | Enable/disable extraction of hash metadata for Bittorrent flows. |
| "ssdp"       | "metadata"                                | enable        | NULL      | NULL      | Enable/disable extraction of ALL metadata for SSDP flows. Note that, unlike all others protocols, for SSDP flows you can't enable/disable a specific metadata |
| "dns"        | "subclassification"                       | disable       | NULL      | NULL      | Enable/disable sub-classification of DNS flows (via query/response domain name). |
| "dns"        | "process_response"                        | enable        | NULL      | NULL      | Enable/disable processing of DNS responses. By default, DNS flows are fully classified after the first request/response pair (or after the first response, if the request is missing). If this parameter is disabled, the flows are fully classified after the first packet, i.e. usually after the first request; in that case, some flow risks are not checked and some metadata are not exported |
| "http"       | "process_response"                        | enable        | NULL      | NULL      | Enable/disable processing of HTTP responses. By default, HTTP flows are usually fully classified after the first request/response pair. If this parameter is disabled, the flows are fully classified after the first request (or after the first response, if the request is missing); in that case, some flow risks are not checked and some metadata are not exported |
| "http"       | "subclassification"                       | enable        | NULL      | NULL      | Enable/disable sub-classification of HTTP flows |
| "ookla"      | "dpi.aggressiveness",                     | 0x01          | 0x00      | 0x01      | Detection aggressiveness for Ookla. The value is a bitmask. Values: 0x0 = disabled; 0x01 = enable heuristic for detection over TLS (via Ookla LRU cache) |
| "zoom"       | "max_packets_extra_dissection"            | 4             | 0         | 255       | After a flow has been classified has Zoom, nDPI might analyse more packets to look for a sub-classification or for metadata. This parameter set the upper limit on the number of these packets  |
| "rtp"        | "search_for_stun"                         | disable       | NULL      | NULL      | After a flow has been classified as RTP or RTCP, nDPI might analyse more packets to look for STUN/DTLS packets, i.e. to try to tell if this flow is a "pure" RTP/RTCP flow or if the RTP/RTCP packets are multiplexed with STUN/DTLS. Useful for proper (sub)classification when the beginning of the flows are not captured or if there are lost packets in the the captured traffic. If enabled, nDPI requires more packets to process for each RTP/RTCP flow. |
| "rtp"        | "max_packets_extra_dissection"            | 32            | 0         | 255       | After a flow has been classified has RTP, nDPI might analyse more packets to look for more metadat. This parameter set the upper limit on the number of these packets  |
| "openvpn"    | "dpi.heuristics",                         | 0x00          | 0         | 0x01      | Enable/disable some heuristics to better detect OpenVPN. The value is a bitmask. Values: 0x0 = disabled; 0x01 = enable heuristic based on op-code frequency. If enabled, some false positives are expected. See: https://www.usenix.org/conference/usenixsecurity22/presentation/xue-diwen |
| "openvpn"    | "dpi.heuristics.num_messages",            | 10            | 0         | 255       | If at least one OpenVPN heuristics is enabled (see `openvpn,"dpi.heuristics"`, this parameter set the maximum number of OpenVPN messages required for each flow. Note that an OpenVPN message may be splitted into multiple (TCP/UDP) packets and that a (TCP/UDP) packet may contains multiple OpenVPN messages. Higher the value, lower the false positive rate but more packets are required by nDPI for processing. |
| "openvpn"    | "subclassification_by_ip"                 | enable        | NULL      | NULL      | Enable/disable sub-classification of OpenVPN flows using server IP. Useful to detect the specific VPN application/app. At the moment, this knob allows to identify: Mullvad, NordVPN. |
| "wireguard"  | "subclassification_by_ip"                 | enable        | NULL      | NULL      | Enable/disable sub-classification of Wireguard flows using server IP. Useful to detect the specific VPN application/app. At the moment, this knob allows to identify: Mullvad, NordVPN. |
| $PROTO_NAME  | "log"                                     | disable       | NULL      | NULL      | Enable/disable logging/debug for specific protocol. Use "any" as protocol name if you want to easily enable/disable logging/debug for all protocols |
| $PROTO_NAME  | "ip_list.load"                            | enable        | NULL      | NULL      | Enable/disable loading of internal list of IP addresses (used for (sub)classification) specific to that protocol. Use "any" as protocol name if you want to easily enable/disable all lists. This knob is valid only for the following protocols: Alibaba, Amazon AWS, Apple, Avast, Blizzard, Bloomberg, Cachefly, Canonical, Cloudflare, DigitalOcean, Discord, Disney+, Dropbox, Edgecast, EpicGames, Ethereum, Facebook, Github, Google, Google Cloud, GoTo, Hotspot Shield, Hulu, Line, Microsoft 365, Microsoft Azure, Microsoft One Drive, Microsoft Outlook, Microsoft Teams, Mullvad, Netflix, NordVPN, Nvidia, OpenDNS, RiotGames, Roblox, Steam, SurfSharkVPN, Teamviewer, Telegram, Tencent, Threema, TOR, Twitch, Twitter, VK, Yandex, Yandex Cloud, Webex, Whatsapp, Zoom |
| $PROTO_NAME  | "monitoring"                              | disable       | NULL      | NULL      | Enable/disable monitoring state for this specific protocol. Use "any" as protocol name if you want to easily enable/disable monitoring feature for all protocols. This knob is valid only for the following protocols: Stun. Monitoring allows nDPI to process the entire flow (i.e. all its packets), without any limits. See doc/monitoring.md for further details |

---
name: "🐞 Report a Bug"
about: Create a report to help us improve nDPI
title: ''
labels: 'bug'
assignees: ''

---


## Describe the bug
A clear and concise description of what the bug is.

### Expected behavior
A clear and concise description of what you expected to happen.
### Obtained behavior
A clear and concise description of what happening.

## nDPI Environment (please complete the following information):
* OS name: [e.g. Ubuntu].
* OS version: [e.g. 18.04]
* Architecture: [e.g. arm64]
* nDPI version or commit hash: [e.g. 4.0-stable or 937357e4bc55610f116f66d15a8e0fc1e260c02c].
* nDPI compilation flags used: if you are building from source [e.g. --with-pcre2 --with-local-libgcrypt].
* Attach the `config.log` file generated after `./configure` ran (if you are building from source).

## How to reproduce the reported bug

### Reproducible using ndpiReader?
- [x] The reported bug is reproducible using ndpiReader.
- [x] The reported bug is not reproducible using ndpiReader.

### If applicable, the used ndpiReader options:

``` shell
ndpiReader -q -t -i wlo1 -T 20 -U 20
```

### If your bug is reproducible using a pcap, please attach a pcap file (or a valid link to download it)

Example: test.pcap

### Steps to reproduce the behavior:
1. Run '...'
2. Set '....'
3. Do '....'
4. See error

## Additional context
Add any other context about the problem here.

name: MSBuild
on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev
    types: [opened, synchronize, reopened]
  release:
    types: [created]
jobs:
  test:
    name: MSBuild ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: ["windows-latest", "windows-11-arm"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Add msbuild to PATH
        uses: microsoft/setup-msbuild@v2

      - name: Download NuGet packages
        run: msbuild windows/nDPI.sln -t:restore -p:RestorePackagesConfig=true

      - name: Build libnDPI only (debug)
        run: msbuild windows/nDPI.sln -t:rebuild -property:Configuration=Debug

      - name: Download Npcap
        run: Invoke-WebRequest -Uri 'https://npcap.com/dist/npcap-1.81.exe' -OutFile 'C:\npcap-1.81.exe'

      - name: Install Npcap
        run: C:\npcap-1.81.exe /S /winpcap_mode=yes

      - name: Build ndpiReader (debug)
        run: msbuild windows/nDPI.sln -t:rebuild -property:Configuration=Debug-ndpiReader

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts-${{ matrix.os }}
          path: windows/bin
          retention-days: 14

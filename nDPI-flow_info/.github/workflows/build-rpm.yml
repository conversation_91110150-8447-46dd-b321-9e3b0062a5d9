name: RPM Build
on:
  workflow_dispatch:
  schedule:
    # At the end of every day
    - cron: '0 0 * * *'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install Ubuntu Prerequisites
      run: |
        sudo apt-get update
        sudo apt-get install autoconf automake libtool pkg-config gettext libjson-c-dev flex bison libpcap-dev

    - name: Configure nDPI
      run: |
        # fake CentOS 7 env
        echo 'CentOS Linux release 7.9.2009 (Core)' | sudo tee /etc/centos-release
        # symlink source directory for ndpi.spec
        ln -sr . $HOME/nDPI
        ./autogen.sh
        cd packages/rpm
        ./configure

    - name: Build RPM package
      run: |
        make -C packages/rpm package

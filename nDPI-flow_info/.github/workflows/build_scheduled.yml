name: Scheduled builds
on:
  workflow_dispatch:
  schedule:
    #At the end of every day
    - cron: '0 0 * * *'

jobs:
  coverage:
    name: Coverage (ubuntu-latest)
    runs-on: ubuntu-latest
    env:
      CFLAGS: -Werror
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake libtool pkg-config gettext libjson-c-dev flex bison libpcap-dev
          sudo apt-get install rrdtool librrd-dev
          sudo apt-get install libpcre3-dev libmaxminddb-dev lcov
          sudo apt-get install wdiff colordiff
      - name: Configure
        run: ./autogen.sh --enable-option-checking=fatal --enable-debug-messages --enable-code-coverage --with-pcre2 --with-maxminddb --enable-tls-sigs
      - name: Build
        run: make all
      - name: Test
        run: |
          make check VERBOSE=1
      - name: Generate Coverage Report
        run: |
          make coverage
      - uses: actions/upload-artifact@v4
        with:
          name: ndpi-coverage-report
          path: coverage_report
          retention-days: 7

  documentation:
    name: Documentation (ubuntu-latest)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python 3.9
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake libtool pkg-config gettext flex bison doxygen
      - name: Configure nDPI library
        run: |
          ./autogen.sh --with-only-libndpi --enable-option-checking=fatal --enable-debug-build
      - name: Generate Documentation
        run: |
          pip install --upgrade pip
          pip install -r doc/requirements.txt
          make doc
          mkdir -vp doc/_build/ndpi-documentation-upload/ndpi-documentation
          mv -v doc/_build/html doc/_build/ndpi-documentation-upload/ndpi-documentation/html
      - uses: actions/upload-artifact@v4
        with:
          name: ndpi-documentation
          path: doc/_build/ndpi-documentation-upload
          retention-days: 7

  performance:
    name: Performance (ubuntu-latest)
    runs-on: ubuntu-latest
    env:
      CFLAGS: -Werror
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install libunwind-dev
          sudo apt-get install autoconf automake libtool pkg-config gettext flex bison libpcap-dev parallel
          sudo apt-get install libgoogle-perftools-dev graphviz
          go install github.com/google/pprof@latest
          sudo ln -s ${HOME}/go/bin/pprof /usr/bin/pprof
          pprof -h
      - name: Configure nDPI library
        run: |
          ./autogen.sh --enable-gprof --enable-option-checking=fatal --with-pcre2 --with-maxminddb --enable-tls-sigs
      - name: Build nDPI library
        run: |
          make -j
      - name: Performance Profiling
        run: |
          NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh
          mkdir ndpi-performance-upload
          for d in $(find ./tests/cfgs/* -type d -maxdepth 0 2>/dev/null) ; do
            PROFILE="$(basename $d)"
            mv -v tests/cfgs/${PROFILE}/result/cpu_profile.png ndpi-performance-upload/${PROFILE}_cpu_profile.png
            mv -v tests/cfgs/${PROFILE}/result/heap_profile.png ndpi-performance-upload/${PROFILE}_heap_profile.png
          done
      - uses: actions/upload-artifact@v4
        with:
          name: ndpi-performance
          path: ndpi-performance-upload
          retention-days: 7

  threadsanitizer:
    name: Thread Sanitizer (ubuntu-latest)
    runs-on: ubuntu-latest
    env:
      CFLAGS: -Wextra -Werror
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake libtool pkg-config gettext flex bison libjson-c-dev libpcap-dev rrdtool librrd-dev parallel
      - name: Configure nDPI
        run: |
          ./autogen.sh --enable-option-checking=fatal --with-thread-sanitizer
      - name: Build nDPI
        run: |
          make -j $(nproc) all
          make -j $(nproc) -C example ndpiSimpleIntegration
          make -j $(nproc) -C rrdtool
      - name: Tests
        run: |
          NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh

  localgcrypt:
    name: Local gcrypt on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    env:
      CFLAGS: -Wextra -Werror -DNDPI_EXTENDED_SANITY_CHECKS
    strategy:
      fail-fast: true
      matrix:
        # macos-14 and 15 are on **ARM64**
        os: ["ubuntu-22.04", "ubuntu-24.04", "macOS-13", "macOS-14", "macOS-15", "ubuntu-22.04-arm", "ubuntu-24.04-arm"]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Ubuntu Prerequisites
        if: startsWith(matrix.os, 'ubuntu')
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake libtool pkg-config gettext flex bison libjson-c-dev libpcap-dev rrdtool librrd-dev parallel libgcrypt20-dev
      - name: Installing MacOS prerequisites
        if: startsWith(matrix.os, 'macOS')
        run: |
          # Avoid (re)installing pkg-config. See: https://github.com/actions/runner-images/issues/10984
          brew install coreutils wdiff colordiff autoconf automake libtool gettext json-c rrdtool parallel libgcrypt
      - name: Configure nDPI
        run: |
          ./autogen.sh --enable-option-checking=fatal --with-local-libgcrypt
      - name: Build nDPI
        run: |
          make -j $(nproc) all
          make -j $(nproc) -C example ndpiSimpleIntegration
          make -j $(nproc) -C rrdtool
      - name: Tests
        run: |
          NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh
          ./tests/do-unit.sh
          ./tests/do-dga.sh

  localgcrypt-windows:
    name: Local gcrypt on ${{ matrix.os }} (msys2)
    runs-on: ${{ matrix.os }}
    env:
      CFLAGS: -Wextra -Werror -DNDPI_EXTENDED_SANITY_CHECKS
    strategy:
      fail-fast: true
      matrix:
        os: ["windows-latest"]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Install Windows msys2 prerequisites
        uses: msys2/setup-msys2@v2
        with:
          msystem: MINGW64
          update: true
          install: git mingw-w64-x86_64-toolchain automake1.16 automake-wrapper autoconf libtool make mingw-w64-x86_64-json-c mingw-w64-x86_64-crt-git mingw-w64-x86_64-pcre mingw-w64-x86_64-libpcap mingw-w64-x86_64-libgcrypt parallel
      - name: Configure nDPI on Windows msys2
        run: |
          msys2 -c './autogen.sh --enable-option-checking=fatal --enable-debug-messages --enable-tls-sigs --disable-npcap --with-local-libgcrypt'
      - name: Build nDPI on Windows msys2
        run: |
          msys2 -c 'make -j all'
          msys2 -c 'ldd ./example/ndpiReader.exe'
      - name: Tests
        run: |
          # Don't know why but lately the script in parallel mode is stuck...
          #msys2 -c 'NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh'
          msys2 -c './tests/do.sh'
          msys2 -c './tests/do-unit.sh'
          msys2 -c './tests/do-dga.sh'


name: Build
on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev
    types: [opened, synchronize, reopened]
  release:
    types: [created]
jobs:
  python-bindings:
    name: Python Bindings (ubuntu-latest)
    runs-on: ubuntu-latest
    env:
      CFLAGS: -Wextra -Werror
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python 3.9
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake libtool pkg-config gettext libjson-c-dev flex bison libpcap-dev
      - name: Build nDPI library
        run: |
          ./autogen.sh
          make -j
          sudo make install
      - name: Generate Python bindings
        run: |
          pip install --upgrade pip
          pip install -r python/requirements.txt
          cd python
          python setup.py install
          cd ..
      - name: Test Python Bindings
        run: |
          cd python
          python tests.py

  test-scripts:
    name: Test Utils (ubuntu-latest)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install python3-netaddr git whois libxml2-utils
      - name: Run Scripts
        run: |
          echo 'Running ./utils/bitcoinnodes.sh'
          ./utils/bitcoinnodes.sh >/dev/null
          echo 'Running ./utils/get_routes_by_asn.sh AS714'
          ./utils/get_routes_by_asn.sh AS714 >/dev/null
          echo 'Running ./utils/update_every_lists.sh'
          ./utils/update_every_lists.sh
          echo 'Checking for changes in the git tree..'
          git update-index --refresh || echo "::warning file=utils/update_every_lists.sh::Please re-run utils/update_every_lists.sh and commit any changes."
          git diff-index --quiet HEAD -- || true

  test:
    name: ${{ matrix.os }} ${{ matrix.compiler }} ${{ matrix.pcre }} ${{ matrix.maxminddb }} ${{ matrix.msan }} ${{ matrix.nBPF }} ${{matrix.global_context}}
    runs-on: ${{ matrix.os }}
    env:
      CC: ${{ matrix.compiler }}
      CFLAGS: -Wextra -Werror -DNDPI_EXTENDED_SANITY_CHECKS
    strategy:
      fail-fast: true
      matrix:
        # macos-14 and 15 are on **ARM64**
        os: ["ubuntu-22.04", "ubuntu-24.04", "macOS-13", "macOS-14", "macOS-15", "ubuntu-22.04-arm", "ubuntu-24.04-arm"]
        compiler: ["cc"]
        pcre: [""]
        maxminddb: [""]
        msan: [""]
        nBPF: [""]
        global_context: [""] # Enable by default
        include:
          - compiler: "gcc-4.9" # "Oldest" gcc easily available. To simulate RHEL7
            os: ubuntu-22.04
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: "--with-sanitizer"
            nBPF: ""
          - compiler: "gcc-14" # "Newest" gcc easily available
            os: ubuntu-24.04
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: "--with-sanitizer"
            nBPF: ""
          - compiler: "clang-12" # "Oldest" clang easily available
            os: ubuntu-22.04
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: "--with-sanitizer"
            nBPF: ""
          - compiler: "clang-18" # "Newest" clang easily available. See also below...
            os: ubuntu-24.04
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: "--with-sanitizer"
            nBPF: ""
          - compiler: "cc"
            os: ubuntu-latest
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: ""
            nBPF: "nBPF"
          - compiler: "cc"
            os: ubuntu-latest
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: ""
            nBPF: ""
            global_context: "--disable-global-context-support"
          - compiler: "cc"
            os: macOS-latest
            pcre: "--with-pcre2"
            maxminddb: "--with-maxminddb"
            msan: ""
            nBPF: ""
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Install Ubuntu Prerequisites
        if: startsWith(matrix.os, 'ubuntu')
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake debhelper libtool pkg-config gettext libjson-c-dev flex bison libpcap-dev
          sudo apt-get install rrdtool librrd-dev parallel
      - name: Install Ubuntu Prerequisites [Mingw-w64] (runs only on ubuntu jobs)
        if: startsWith(matrix.os, 'ubuntu') && !endsWith(matrix.os, 'arm') && !startsWith(matrix.msan, '--with-') && !startsWith(matrix.nBPF, 'nBPF') && !startsWith(matrix.global_context, '--without') # Only on a few "standard" builds
        run: |
          sudo apt-get install gcc-mingw-w64 libc6-dev
      - name: Install Ubuntu Prerequisites (libpcre2)
        if: startsWith(matrix.os, 'ubuntu') && startsWith(matrix.pcre, '--with-pcre2')
        run: |
          sudo apt-get install libpcre3-dev
      - name: Install Ubuntu Prerequisites (maxminddb)
        if: startsWith(matrix.os, 'ubuntu') && startsWith(matrix.maxminddb, '--with-maxminddb')
        run: |
          sudo apt-get install libmaxminddb-dev
      - name: Install Ubuntu Prerequisites (nBPF)
        if: startsWith(matrix.os, 'ubuntu') && startsWith(matrix.nBPF, 'nBPF')
        run: |
          git clone https://github.com/ntop/PF_RING.git ../PF_RING
          cd ../PF_RING/userland/nbpf
          ./configure
          make
          cd -
      - name: Setup Ubuntu specified compiler
        if: startsWith(matrix.os, 'ubuntu-22.04') && ! startsWith(matrix.compiler, 'cc')
        run: |
          # For gcc-4.9 (on ubuntu-22.04)
          echo "deb http://dk.archive.ubuntu.com/ubuntu/ xenial main" | sudo tee -a /etc/apt/sources.list
          echo "deb http://dk.archive.ubuntu.com/ubuntu/ xenial universe" | sudo tee -a /etc/apt/sources.list
          sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 40976EAF437D05B5
          sudo apt-get update
          sudo apt-get install ${{ matrix.compiler }}
      - name: Installing MacOS prerequisites
        if: startsWith(matrix.os, 'macOS')
        run: |
          # Avoid (re)installing pkg-config. See: https://github.com/actions/runner-images/issues/10984
          brew install coreutils wdiff colordiff autoconf automake libtool gettext json-c rrdtool parallel
      - name: Install MacOS Prerequisites (libpcre2)
        if: startsWith(matrix.os, 'macOS') && startsWith(matrix.pcre, '--with-pcre2')
        run: |
          brew install pcre
      - name: Install MacOS Prerequisites (maxminddb)
        if: startsWith(matrix.os, 'macOS') && startsWith(matrix.maxminddb, '--with-maxminddb')
        run: |
          brew install libmaxminddb
      - name: Configure nDPI
        run: |
          ./autogen.sh --enable-option-checking=fatal --enable-debug-messages ${{ matrix.msan }} ${{ matrix.pcre }} ${{ matrix.maxminddb }} --enable-tls-sigs ${{ matrix.global_context}}
      - name: Build nDPI
        run: |
          make -j all
          make -C example ndpiSimpleIntegration
          make -C rrdtool
      - name: Print nDPI long help
        run: |
          cd ./example && ./ndpiReader -H
      - name: Install nDPI
        run: |
          DESTDIR=/tmp/ndpi make install
          ls -alhHR /tmp/ndpi
      - name: Test nDPI [SYMBOLS]
        if: startsWith(matrix.os, 'ubuntu') && !endsWith(matrix.os, 'arm') && !startsWith(matrix.msan, '--with-') && !startsWith(matrix.nBPF, 'nBPF') && !startsWith(matrix.global_context, '--without') # Only on a few "standard" builds
        run: |
          ./utils/check_symbols.sh || { FAILED=$?; echo "::error file=${NDPI_LIB}::Unwanted libc symbols found: ${FAILED}. Please make sure to use only ndpi_malloc/ndpi_calloc/ndpi_realloc/ndpi_free wrapper instead of malloc/calloc/realloc/free."; false; }
        env:
          NDPI_LIB: src/lib/libndpi.a
      - name: Test nDPI [DIFF]
        run: |
          NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh
      - name: Test nDPI [UNIT]
        run: |
          ./tests/do-unit.sh
      - name: Test nDPI [DGA]
        run: |
          ./tests/do-dga.sh
      - name: Generate/Verify tarball
        if: startsWith(matrix.os, 'ubuntu-latest')
        run: |
          make dist
          ./utils/verify_dist_tarball.sh
      - name: Build Debian/Ubuntu package
        if: startsWith(matrix.os, 'ubuntu-24.04') && !endsWith(matrix.os, 'arm') && startsWith(matrix.compiler, 'cc')
        run: |
          cd packages/ubuntu
          ./configure --enable-no-sign
          make
          cd ../..
      - name: Build nDPI [Mingw-w64] (runs only on ubuntu jobs)
        if: startsWith(matrix.os, 'ubuntu') && !endsWith(matrix.os, 'arm') && !startsWith(matrix.msan, '--with-') && !startsWith(matrix.nBPF, 'nBPF') && !startsWith(matrix.global_context, '--without') # Only on a few "standard" builds
        run: |
          make distclean
          ./autogen.sh --enable-option-checking=fatal --enable-debug-messages --enable-tls-sigs --host=x86_64-w64-mingw32
          make -j $(nproc) all
        env:
          CC:

  test-lto-gold:
    # Options used by oss-fuzz: we only want to check that everything compile fine; no need to run the tests
    name: LTO and Gold Linker ${{ matrix.compiler }}
    runs-on: ubuntu-24.04
    env:
      CC: ${{ matrix.compiler }}
      CFLAGS: -Wextra -Werror -DNDPI_EXTENDED_SANITY_CHECKS
    strategy:
      fail-fast: true
      matrix:
        compiler: ["gcc-14", "clang-18"] # "Newest" gcc/clang easily available
        include:
          - compiler: "gcc-14"
            ar: "ar"
            ranlib: "ranlib"
          - compiler: "clang-18"
            ar: "llvm-ar-18"
            ranlib: "llvm-ranlib-18"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Install Ubuntu Prerequisites
        run: |
          sudo apt-get update
          sudo apt-get install autoconf automake debhelper libtool pkg-config gettext libjson-c-dev flex bison libpcap-dev rrdtool librrd-dev parallel
      - name: Configure nDPI
        run: |
          AR=${{ matrix.ar }} RANLIB=${{ matrix.ranlib }} ./autogen.sh --enable-option-checking=fatal --enable-debug-messages --with-sanitizer --with-lto-and-gold-linker
      - name: Build nDPI
        run: |
          make -j $(nproc) all
          make -j $(nproc) -C example ndpiSimpleIntegration
          make -j $(nproc) -C rrdtool
      - name: Print nDPI long help
        run: |
          cd ./example && ./ndpiReader -H

  test-windows:
    name: ${{ matrix.os }} (msys2)
    runs-on: ${{ matrix.os }}
    env:
      CFLAGS: -Wextra -Werror -DNDPI_EXTENDED_SANITY_CHECKS
    strategy:
      fail-fast: true
      matrix:
        os: ["windows-latest"]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Install Windows msys2 prerequisites
        uses: msys2/setup-msys2@v2
        with:
          msystem: MINGW64
          update: true
          install: git mingw-w64-x86_64-toolchain automake1.16 automake-wrapper autoconf libtool make mingw-w64-x86_64-json-c mingw-w64-x86_64-crt-git mingw-w64-x86_64-pcre mingw-w64-x86_64-libpcap parallel
      - name: Configure nDPI on Windows msys2
        run: |
          msys2 -c './autogen.sh --enable-option-checking=fatal --enable-debug-messages --enable-tls-sigs --disable-npcap'
      - name: Build nDPI on Windows msys2
        run: |
          msys2 -c 'make -j all'
          msys2 -c 'ldd ./example/ndpiReader.exe'
      - name: Tests
        run: |
          # Don't know why but lately the script in parallel mode is stuck...
          #msys2 -c 'NDPI_FORCE_PARALLEL_UTESTS=1 NDPI_SKIP_PARALLEL_BAR=1 ./tests/do.sh'
          msys2 -c './tests/do.sh'
          msys2 -c './tests/do-unit.sh'
          msys2 -c './tests/do-dga.sh'

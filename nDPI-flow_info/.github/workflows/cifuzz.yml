name: CIFuzz
on: [push, pull_request]
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        sanitizer: [address, undefined, memory]
    steps:
      - name: Build Fuzzers - ${{ matrix.sanitizer }}
        uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
        with:
          oss-fuzz-project-name: 'ndpi'
          dry-run: false
          sanitizer: ${{ matrix.sanitizer }}
      - name: Run Fuzzers - ${{ matrix.sanitizer }}
        uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
        with:
          oss-fuzz-project-name: 'ndpi'
          fuzz-seconds: 600
          dry-run: false
          sanitizer: ${{ matrix.sanitizer }}
      - name: Check Crash (fails when a crash is detected)
        # Run Fuzzers return success even when setting dry-run to false.
        # A temporal workaround is to trigger failure manually if we fing crash files.
        run: |
          exit $(ls out/artifacts |wc -l)
      - name: Upload Crash (upload detected crash as artifacts)
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: ${{ matrix.sanitizer }}-artifacts
          path: ./out/artifacts
